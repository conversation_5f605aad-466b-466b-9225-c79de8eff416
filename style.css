:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08);
  /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08);
  /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08);
  /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08);
  /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08);
  /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08);
  /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08);
  /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08);
  /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 119, 124, 124;
    --color-teal-300-rgb: 50, 184, 198;
    --color-gray-300-rgb: 167, 169, 169;
    --color-gray-200-rgb: 245, 245, 245;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(29, 78, 216, 0.15);
    /* Dark blue */
    --color-bg-2: rgba(180, 83, 9, 0.15);
    /* Dark yellow */
    --color-bg-3: rgba(21, 128, 61, 0.15);
    /* Dark green */
    --color-bg-4: rgba(185, 28, 28, 0.15);
    /* Dark red */
    --color-bg-5: rgba(107, 33, 168, 0.15);
    /* Dark purple */
    --color-bg-6: rgba(194, 65, 12, 0.15);
    /* Dark orange */
    --color-bg-7: rgba(190, 24, 93, 0.15);
    /* Dark pink */
    --color-bg-8: rgba(8, 145, 178, 0.15);
    /* Dark cyan */

    /* Semantic Color Tokens (Dark Mode) */
    --color-background: var(--color-charcoal-700);
    --color-surface: var(--color-charcoal-800);
    --color-text: var(--color-gray-200);
    --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
    --color-primary: var(--color-teal-300);
    --color-primary-hover: var(--color-teal-400);
    --color-primary-active: var(--color-teal-800);
    --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
    --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
    --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
    --color-border: rgba(var(--color-gray-400-rgb), 0.3);
    --color-error: var(--color-red-400);
    --color-success: var(--color-teal-300);
    --color-warning: var(--color-orange-400);
    --color-info: var(--color-gray-300);
    --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
    --color-btn-primary-text: var(--color-slate-900);
    --color-card-border: rgba(var(--color-gray-400-rgb), 0.2);
    --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: var(--color-teal-300-rgb);
    --color-error-rgb: var(--color-red-400-rgb);
    --color-warning-rgb: var(--color-orange-400-rgb);
    --color-info-rgb: var(--color-gray-300-rgb);
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  /* RGB versions for opacity control (dark mode) */
  --color-gray-400-rgb: 119, 124, 124;
  --color-teal-300-rgb: 50, 184, 198;
  --color-gray-300-rgb: 167, 169, 169;
  --color-gray-200-rgb: 245, 245, 245;

  /* Colorful background palette - Dark Mode */
  --color-bg-1: rgba(29, 78, 216, 0.15);
  /* Dark blue */
  --color-bg-2: rgba(180, 83, 9, 0.15);
  /* Dark yellow */
  --color-bg-3: rgba(21, 128, 61, 0.15);
  /* Dark green */
  --color-bg-4: rgba(185, 28, 28, 0.15);
  /* Dark red */
  --color-bg-5: rgba(107, 33, 168, 0.15);
  /* Dark purple */
  --color-bg-6: rgba(194, 65, 12, 0.15);
  /* Dark orange */
  --color-bg-7: rgba(190, 24, 93, 0.15);
  /* Dark pink */
  --color-bg-8: rgba(8, 145, 178, 0.15);
  /* Dark cyan */

  /* Semantic Color Tokens (Dark Mode) */
  --color-background: var(--color-charcoal-700);
  --color-surface: var(--color-charcoal-800);
  --color-text: var(--color-gray-200);
  --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
  --color-primary: var(--color-teal-300);
  --color-primary-hover: var(--color-teal-400);
  --color-primary-active: var(--color-teal-800);
  --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
  --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
  --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
  --color-border: rgba(var(--color-gray-400-rgb), 0.3);
  --color-error: var(--color-red-400);
  --color-success: var(--color-teal-300);
  --color-warning: var(--color-orange-400);
  --color-info: var(--color-gray-300);
  --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
  --color-btn-primary-text: var(--color-slate-900);
  --color-card-border: rgba(var(--color-gray-400-rgb), 0.15);
  --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
  --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: var(--color-teal-300-rgb);
  --color-error-rgb: var(--color-red-400-rgb);
  --color-warning-rgb: var(--color-orange-400-rgb);
  --color-info-rgb: var(--color-gray-300-rgb);
}

[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, #ff6b7a 100%);
  color: white;
  border: none;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
}

.btn--primary:hover {
  background: linear-gradient(135deg, #ff6b7a 0%, var(--color-primary) 100%);
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.4);
  transform: translateY(-1px);
}

.btn--primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(var(--color-success-rgb, 33, 128, 141),
      var(--status-bg-opacity));
  color: var(--color-success);
  border: 1px solid rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(var(--color-error-rgb, 192, 21, 47),
      var(--status-bg-opacity));
  color: var(--color-error);
  border: 1px solid rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(var(--color-warning-rgb, 168, 75, 47),
      var(--status-bg-opacity));
  color: var(--color-warning);
  border: 1px solid rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(var(--color-info-rgb, 98, 108, 113),
      var(--status-bg-opacity));
  color: var(--color-info);
  border: 1px solid rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: var(--space-4);
}

.gap-8 {
  gap: var(--space-8);
}

.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}

.mt-8 {
  margin-top: var(--space-8);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}

.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}

.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}

.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}

.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}

.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}

.hidden {
  display: none !important;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2') format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* IsotopeAI - YT Tracker Styles */

/* Layout Structure */
#app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--color-background);
}

/* Header */
.app-header {
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-card-border);
  padding: var(--space-16) var(--space-24);
  box-shadow: var(--shadow-sm);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: var(--container-xl);
  margin: 0 auto;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.app-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.app-title .fab {
  color: var(--color-red-400);
  margin-right: var(--space-8); /* Added space for icon */
}

.playlist-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

#currentPlaylistTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

#globalStats {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Upload Section */
.upload-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-32);
}

.upload-card {
  max-width: 500px;
  width: 100%;
}

.upload-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-24);
}

.url-validation {
  margin-top: var(--space-8);
  font-size: var(--font-size-sm);
  height: var(--space-20);
}

.url-validation.valid {
  color: var(--color-success);
}

.url-validation.invalid {
  color: var(--color-error);
}

/* Main Content Layout */
.main-content {
  display: grid;
  grid-template-columns: 300px 1fr 280px;
  gap: var(--space-16);
  flex: 1;
  padding: var(--space-24);
  max-width: var(--container-xl);
  margin: 0 auto;
  width: 100%;
}

/* Sidebars */
.sidebar {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  height: fit-content;
  max-height: calc(100vh - 200px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

.sidebar-header h3 {
  margin: 0 0 var(--space-12) 0;
  font-size: var(--font-size-xl);
}

.playlist-controls {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.filter-buttons {
  display: flex;
  gap: var(--space-8);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
}

.filter-btn.active {
  background-color: var(--color-primary);
  color: var(--color-btn-primary-text);
}

/* Video List */
.video-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-8);
}

.video-item {
  display: flex;
  align-items: center;
  padding: var(--space-12);
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: background-color var(--duration-fast) var(--ease-standard);
  margin-bottom: var(--space-8);
  border: 1px solid transparent;
}

.video-item:hover {
  background-color: var(--color-secondary);
}

.video-item.active {
  background-color: var(--color-bg-1);
  border-color: var(--color-primary);
}

.video-thumbnail {
  width: 60px;
  height: 34px;
  border-radius: var(--radius-sm);
  background-color: var(--color-bg-2);
  margin-right: var(--space-16); /* Increased space for icon */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  overflow: hidden;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-details {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.video-status {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.status-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  margin-right: var(--space-4); /* Added space for icon */
}

.status-icon.completed {
  background-color: var(--color-success);
  color: white;
}

.status-icon.progress {
  background-color: var(--color-warning);
  color: white;
}

.status-icon.unwatched {
  background-color: var(--color-border);
  color: var(--color-text-secondary);
}

/* Video Player Section */
.video-player-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.video-player-container {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.video-player {
  aspect-ratio: 16/9;
  background-color: var(--color-bg-8);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.video-placeholder {
  text-align: center;
  color: var(--color-text-secondary);
}

.video-placeholder .fab {
  font-size: 48px;
  margin-bottom: var(--space-16);
  color: var(--color-red-400);
}

.video-controls {
  padding: var(--space-16);
  border-top: 1px solid var(--color-card-border-inner);
}

.control-row {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

.progress-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.progress-bar {
  height: 6px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
  position: relative;
  cursor: pointer;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width var(--duration-fast) var(--ease-standard);
  width: 0%;
}

.progress-handle {
  width: 12px;
  height: 12px;
  background-color: var(--color-primary);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  left: 0%;
  cursor: grab;
  opacity: 0;
  transition: opacity var(--duration-fast) var(--ease-standard);
}

.progress-bar:hover .progress-handle {
  opacity: 1;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.video-info {
  padding: var(--space-16);
  border-top: 1px solid var(--color-card-border-inner);
  background-color: var(--color-background);
}

#currentVideoTitle {
  margin: 0 0 var(--space-8) 0;
  font-size: var(--font-size-xl);
}

.video-meta {
  display: flex;
  gap: var(--space-24);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Analytics Panel */
.analytics-panel {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
  height: fit-content;
}

.analytics-card {
  background-color: var(--color-surface);
}

.analytics-card .card__header {
  background-color: var(--color-bg-1);
  border-bottom: 1px solid var(--color-card-border-inner);
}

.analytics-card .card__header h4 {
  margin: 0;
  font-size: var(--font-size-lg);
}

/* Progress Overview */
.progress-overview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-16);
}

.circular-progress {
  width: 80px;
  height: 80px;
  position: relative;
}

.progress-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(var(--color-primary) 0deg, var(--color-secondary) 0deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle::before {
  content: '';
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--color-surface);
  position: absolute;
}

.progress-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  z-index: 1;
}

.progress-stats {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-8) 0;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.stat-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

/* Deadline Management */
.deadline-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.deadline-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.status-indicator {
  margin-top: var(--space-8);
}

/* Video Progress Chart */
.video-progress-chart {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.progress-item {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  font-size: var(--font-size-xs);
}

.progress-item-label {
  min-width: 60px;
  color: var(--color-text-secondary);
}

.progress-item-bar {
  flex: 1;
  height: 4px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-item-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
}

.progress-item-percentage {
  min-width: 35px;
  text-align: right;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Settings Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-16) var(--space-20);
  border-bottom: 1px solid var(--color-card-border-inner);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-xl);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-4);
  border-radius: var(--radius-sm);
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.modal-close:hover {
  background-color: var(--color-secondary);
}

.modal-body {
  padding: var(--space-20);
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-16) var(--space-20);
  border-top: 1px solid var(--color-card-border-inner);
  background-color: var(--color-background);
}

.settings-section {
  margin-bottom: var(--space-24);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section h4 {
  margin: 0 0 var(--space-16) 0;
  font-size: var(--font-size-lg);
  color: var(--color-text);
}

.setting-item {
  margin-bottom: var(--space-16);
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-actions {
  display: flex;
  gap: var(--space-12);
  margin-top: var(--space-16);
}

/* Checkbox styling */
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

.checkbox-label input[type="checkbox"] {
  margin-right: var(--space-8);
  width: 16px;
  height: 16px;
  accent-color: var(--color-primary);
}

.checkmark {
  margin-left: var(--space-8);
}

/* Footer */
.app-footer {
  background-color: var(--color-surface);
  border-top: 1px solid var(--color-card-border);
  padding: var(--space-16) var(--space-24);
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: var(--container-xl);
  margin: 0 auto;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.footer-links {
  display: flex;
  gap: var(--space-16);
}

.footer-links a {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

.footer-links a:hover {
  color: var(--color-primary);
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-primary);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-standard);
}

.slide-in {
  animation: slideIn var(--duration-normal) var(--ease-standard);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 280px 1fr 260px;
    gap: var(--space-12);
  }
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
    gap: var(--space-16);
  }

  .sidebar {
    max-height: 300px;
  }

  .sidebar-left {
    order: 1;
  }

  .video-player-section {
    order: 2;
  }

  .sidebar-right {
    order: 3;
  }

  .analytics-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-16);
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--space-12);
    text-align: center;
  }

  .header-left {
    align-items: center;
  }

  .app-title {
    font-size: var(--font-size-2xl);
  }

  .main-content {
    padding: var(--space-16);
  }

  .control-row {
    flex-direction: column;
    gap: var(--space-12);
  }

  .progress-container {
    order: -1;
  }

  .filter-buttons {
    justify-content: center;
  }

  .footer-content {
    flex-direction: column;
    gap: var(--space-8);
    text-align: center;
  }

  .modal-content {
    margin: var(--space-16);
    max-height: calc(100vh - 32px);
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: var(--font-size-xl);
  }

  .video-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-8);
  }

  .video-thumbnail {
    width: 100%;
    height: 60px;
    margin-right: 0;
  }

  .btn {
    font-size: var(--font-size-sm);
    padding: var(--space-6) var(--space-12);
  }

  .setting-actions {
    flex-direction: column;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-border: var(--color-text);
    --color-card-border: var(--color-text);
  }

  .btn--outline {
    border-width: 2px;
  }

  .form-control {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus improvements for accessibility */
.btn:focus-visible,
.form-control:focus-visible,
.video-item:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Print styles */
@media print {

  .app-header,
  .app-footer,
  .video-player-section,
  .btn {
    display: none !important;
  }

  .main-content {
    display: block !important;
  }

  .sidebar {
    box-shadow: none !important;
    border: 1px solid #000 !important;
    margin-bottom: 1rem !important;
  }
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.stat-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

/* Deadline Section */
.deadline-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.deadline-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.status-indicator {
  text-align: center;
}

/* Video Progress Chart */
.video-progress-chart {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
  max-height: 200px;
  overflow-y: auto;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  font-size: var(--font-size-xs);
}

.progress-item-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--color-text-secondary);
}

.progress-item-bar {
  width: 60px;
  height: 4px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-item-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
}

.progress-item-percentage {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  min-width: 30px;
  text-align: right;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal.hidden {
  display: none;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-20);
  border-bottom: 1px solid var(--color-card-border-inner);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-2xl);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-4);
  border-radius: var(--radius-sm);
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.modal-close:hover {
  background-color: var(--color-secondary);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-20);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-12);
  padding: var(--space-20);
  border-top: 1px solid var(--color-card-border-inner);
}

/* Settings */
.settings-section {
  margin-bottom: var(--space-24);
}

.settings-section h4 {
  margin: 0 0 var(--space-16) 0;
  font-size: var(--font-size-lg);
  color: var(--color-text);
}

.setting-item {
  margin-bottom: var(--space-16);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  cursor: pointer;
  font-size: var(--font-size-base);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--color-border);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--duration-fast) var(--ease-standard);
}

.checkbox-label input[type="checkbox"]:checked+.checkmark {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox-label input[type="checkbox"]:checked+.checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.setting-actions {
  display: flex;
  gap: var(--space-12);
  margin-top: var(--space-16);
}

/* Footer */
.app-footer {
  background-color: var(--color-surface);
  border-top: 1px solid var(--color-card-border);
  padding: var(--space-16) var(--space-24);
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: var(--container-xl);
  margin: 0 auto;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.footer-links {
  display: flex;
  gap: var(--space-16);
}

.footer-links a {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

.footer-links a:hover {
  color: var(--color-primary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 250px 1fr 250px;
    gap: var(--space-12);
    padding: var(--space-16);
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--space-16);
    align-items: flex-start;
  }

  .playlist-info {
    align-self: stretch;
  }

  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: var(--space-16);
  }

  .sidebar {
    max-height: none;
  }

  .video-list {
    max-height: 300px;
  }

  .analytics-panel {
    flex-direction: row;
    overflow-x: auto;
    gap: var(--space-16);
  }

  .analytics-card {
    min-width: 250px;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: var(--space-12) var(--space-16);
  }

  .app-title {
    font-size: var(--font-size-2xl);
  }

  .main-content {
    padding: var(--space-12);
  }

  .control-row {
    flex-direction: column;
    gap: var(--space-12);
  }

  .progress-container {
    order: -1;
  }

  .modal-content {
    width: 95%;
  }

  .footer-content {
    flex-direction: column;
    gap: var(--space-12);
    text-align: center;
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-error {
  color: var(--color-error);
}

.bg-success {
  background-color: rgba(var(--color-success-rgb), 0.1);
}

.bg-warning {
  background-color: rgba(var(--color-warning-rgb), 0.1);
}

.bg-error {
  background-color: rgba(var(--color-error-rgb), 0.1);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-standard);
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-32);
  text-align: center;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-card-border);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-secondary);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-16) auto;
}

.loading-spinner p {
  margin: 0;
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: var(--space-20);
  right: var(--space-20);
  z-index: 1500;
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
  max-width: 400px;
}

.toast {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  padding: var(--space-12) var(--space-16);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: var(--space-12);
  animation: slideInRight 0.3s var(--ease-standard);
  position: relative;
  overflow: hidden;
}

.toast::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--color-primary);
}

.toast--success::before {
  background-color: var(--color-success);
}

.toast--error::before {
  background-color: var(--color-error);
}

.toast--warning::before {
  background-color: var(--color-warning);
}

.toast--info::before {
  background-color: var(--color-info);
}

.toast-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.toast--success .toast-icon {
  color: var(--color-success);
}

.toast--error .toast-icon {
  color: var(--color-error);
}

.toast--warning .toast-icon {
  color: var(--color-warning);
}

.toast--info .toast-icon {
  color: var(--color-info);
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  margin: 0 0 var(--space-4) 0;
  color: var(--color-text);
}

.toast-message {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
}

.toast-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-4);
  border-radius: var(--radius-sm);
  transition: background-color var(--duration-fast) var(--ease-standard);
  flex-shrink: 0;
}

.toast-close:hover {
  background-color: var(--color-secondary);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast.removing {
  animation: slideOutRight 0.3s var(--ease-standard) forwards;
}

/* Enhanced video player with real YouTube embed */
.video-player iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: var(--radius-base);
}



/* Progress indicator for video items */
.video-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width var(--duration-normal) var(--ease-standard);
  width: var(--progress-width, 0%);
}

.video-item {
  position: relative;
  overflow: hidden;
}

/* Enhanced focus states */
.video-item:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
}

/* Smooth transitions for all interactive elements */
.btn,
.form-control,
.video-item,
.card,
.progress-bar,
.status-icon {
  transition: all var(--duration-fast) var(--ease-standard);
}

/* Enhanced hover effects */
.video-item:hover .video-thumbnail {
  transform: scale(1.05);
}

.video-thumbnail {
  transition: transform var(--duration-fast) var(--ease-standard);
}

/* Better scrollbar styling */
.video-list::-webkit-scrollbar,
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.video-list::-webkit-scrollbar-track,
.modal-body::-webkit-scrollbar-track {
  background: var(--color-secondary);
  border-radius: var(--radius-full);
}

.video-list::-webkit-scrollbar-thumb,
.modal-body::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-full);
}

.video-list::-webkit-scrollbar-thumb:hover,
.modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* Mobile-specific enhancements */
@media (max-width: 768px) {
  .toast-container {
    top: var(--space-16);
    right: var(--space-16);
    left: var(--space-16);
    max-width: none;
  }



  .loading-spinner {
    padding: var(--space-24);
  }

  .spinner {
    width: 32px;
    height: 32px;
  }
}

/* Dark mode specific enhancements */
@media (prefers-color-scheme: dark) {
  .loading-overlay {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

[data-color-scheme="dark"] .loading-overlay {
  background-color: rgba(0, 0, 0, 0.8);
}
/* D
ashboard Styles */

/* Dashboard Navigation */
.dashboard-nav {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  font-size: var(--font-size-sm);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

.nav-link i { /* Targeting the icon within nav-link */
  margin-right: var(--space-4);
}

.nav-link:hover {
  color: var(--color-primary);
}

.nav-link.active {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.nav-separator {
  color: var(--color-text-secondary);
}

/* Dashboard Main Layout */
.dashboard-main {
  flex: 1;
  padding: var(--space-24);
  max-width: var(--container-xl);
  margin: 0 auto;
  width: 100%;
}

/* Overview Stats Section */
.overview-section {
  margin-bottom: var(--space-32);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-16);
}

.stat-card {
  background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-teal-500-rgb), 0.05) 100%);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  display: flex;
  align-items: center;
  gap: var(--space-16);
  transition: all var(--duration-normal) var(--ease-standard);
  box-shadow: var(--shadow-sm);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-base);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-xl);
  margin-right: var(--space-12); /* Added space for icon */
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  line-height: 1;
  margin-bottom: var(--space-4);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Dashboard Grid Layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-32);
}

.dashboard-section {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.section-header {
  padding: var(--space-20) var(--space-24);
  border-bottom: 1px solid var(--color-card-border-inner);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, var(--color-bg-1) 0%, var(--color-bg-2) 100%);
}

.section-header h2 {
  margin: 0;
  font-size: var(--font-size-2xl);
  color: var(--color-text);
}

.section-filters {
  display: flex;
  gap: var(--space-8);
}

/* Playlists Grid */
.playlists-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-20);
  padding: var(--space-24);
}

.playlist-card {
  background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-teal-500-rgb), 0.02) 100%);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  transition: all var(--duration-normal) var(--ease-standard);
  cursor: pointer;
}

.playlist-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.playlist-header {
  display: flex;
  align-items: center;
  gap: var(--space-16);
  margin-bottom: var(--space-16);
}

.playlist-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-base);
  background: linear-gradient(135deg, var(--color-red-400) 0%, var(--color-red-500) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-2xl);
  margin-right: var(--space-12); /* Added space for icon */
}

.playlist-info {
  flex: 1;
  min-width: 0;
}

.playlist-title {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.playlist-channel {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.playlist-stats {
  margin-bottom: var(--space-16);
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
  font-size: var(--font-size-sm);
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-row .stat-label {
  color: var(--color-text-secondary);
}

.stat-row .stat-value {
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
}

.playlist-progress {
  margin-bottom: var(--space-16);
}

.progress-bar-mini {
  height: 4px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill-mini {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
}

.playlist-actions {
  display: flex;
  gap: var(--space-8);
}

/* Videos Grid */
.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-20);
  padding: var(--space-24);
}

.video-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
  cursor: pointer;
}

.video-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.video-card.completed {
  border-left: 4px solid var(--color-success);
}

.video-card.in-progress {
  border-left: 4px solid var(--color-warning);
}

.video-card.unwatched {
  border-left: 4px solid var(--color-border);
}

.video-thumbnail-large {
  position: relative;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.video-thumbnail-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--duration-normal) var(--ease-standard);
}

.video-card:hover .video-thumbnail-large img {
  transform: scale(1.05);
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-standard);
}

.video-card:hover .video-overlay {
  opacity: 1;
}

.play-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text);
  font-size: var(--font-size-xl);
}

.video-progress-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: rgba(0, 0, 0, 0.3);
}

.progress-bar-overlay {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  transition: width var(--duration-normal) var(--ease-standard);
}

.video-card-content {
  padding: var(--space-16);
}

.video-card-title {
  margin: 0 0 var(--space-8) 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  line-height: var(--line-height-tight);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.video-card-playlist {
  margin: 0 0 var(--space-12) 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

/* Analytics Section */
.analytics-section {
  grid-column: 1 / -1;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-20);
  padding: var(--space-24);
}

.analytics-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.analytics-card .card-header {
  padding: var(--space-16) var(--space-20);
  background: linear-gradient(135deg, var(--color-bg-3) 0%, var(--color-bg-4) 100%);
  border-bottom: 1px solid var(--color-card-border-inner);
}

.analytics-card .card-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--color-text);
}

.analytics-card .card-body {
  padding: var(--space-20);
}

/* Progress Chart */
.progress-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-20);
}

.circular-progress-large {
  width: 120px;
  height: 120px;
  position: relative;
}

.progress-circle-large {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(var(--color-primary) 0deg, var(--color-secondary) 0deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle-large::before {
  content: '';
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--color-surface);
  position: absolute;
}

.progress-text-large {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  position: relative;
  z-index: 1;
}

.progress-details {
  width: 100%;
}

.progress-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
  font-size: var(--font-size-sm);
}

.progress-item:last-child {
  margin-bottom: 0;
}

.progress-label {
  color: var(--color-text-secondary);
}

.progress-value {
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
}

/* Weekly Activity Chart */
.activity-chart {
  display: flex;
  justify-content: space-between;
  align-items: end;
  height: 120px;
  gap: var(--space-8);
}

.activity-day {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
}

.activity-bar {
  width: 100%;
  min-height: 4px;
  background: linear-gradient(180deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  border-radius: var(--radius-sm);
  transition: all var(--duration-normal) var(--ease-standard);
}

.activity-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.activity-value {
  font-size: var(--font-size-xs);
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
}

/* Streak Display */
.streak-display {
  text-align: center;
}

.streak-number {
  font-size: 48px;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  line-height: 1;
  margin-bottom: var(--space-8);
}

.streak-label {
  font-size: var(--font-size-lg);
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-4);
}

.streak-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Goals List */
.goals-list {
  margin-bottom: var(--space-16);
}

.goal-item {
  padding: var(--space-16);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  margin-bottom: var(--space-12);
  transition: all var(--duration-normal) var(--ease-standard);
}

.goal-item:last-child {
  margin-bottom: 0;
}

.goal-item.urgent {
  border-left: 4px solid var(--color-error);
  background-color: rgba(var(--color-error-rgb), 0.05);
}

.goal-item.warning {
  border-left: 4px solid var(--color-warning);
  background-color: rgba(var(--color-warning-rgb), 0.05);
}

.goal-item.normal {
  border-left: 4px solid var(--color-success);
  background-color: rgba(var(--color-success-rgb), 0.05);
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-12);
}

.goal-title {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.goal-deadline {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.goal-progress {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.goal-progress .progress-bar-mini {
  flex: 1;
}

.goal-percentage {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  min-width: 40px;
  text-align: right;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-20);
}

.modal.hidden {
  display: none;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 500px;
  position: relative;
  z-index: 1;
}

.modal-header {
  padding: var(--space-20) var(--space-24);
  border-bottom: 1px solid var(--color-card-border-inner);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-xl);
  color: var(--color-text);
}

.modal-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-4);
  border-radius: var(--radius-sm);
  transition: all var(--duration-fast) var(--ease-standard);
}

.modal-close:hover {
  color: var(--color-text);
  background-color: var(--color-secondary);
}

.modal-body {
  padding: var(--space-24);
}

.modal-footer {
  padding: var(--space-20) var(--space-24);
  border-top: 1px solid var(--color-card-border-inner);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-12);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: var(--space-20);
  right: var(--space-20);
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.toast {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-lg);
  padding: var(--space-16);
  display: flex;
  align-items: center;
  gap: var(--space-12);
  min-width: 300px;
  max-width: 400px;
  animation: slideIn 0.3s var(--ease-standard);
}

.toast.removing {
  animation: slideOut 0.3s var(--ease-standard);
}

.toast--success {
  border-left: 4px solid var(--color-success);
}

.toast--error {
  border-left: 4px solid var(--color-error);
}

.toast--warning {
  border-left: 4px solid var(--color-warning);
}

.toast--info {
  border-left: 4px solid var(--color-info);
}

.toast-icon {
  font-size: var(--font-size-lg);
}

.toast--success .toast-icon {
  color: var(--color-success);
}

.toast--error .toast-icon {
  color: var(--color-error);
}

.toast--warning .toast-icon {
  color: var(--color-warning);
}

.toast--info .toast-icon {
  color: var(--color-info);
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.toast-message {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.toast-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-4);
  border-radius: var(--radius-sm);
  transition: all var(--duration-fast) var(--ease-standard);
}

.toast-close:hover {
  color: var(--color-text);
  background-color: var(--color-secondary);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-main {
    padding: var(--space-16);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-12);
  }
  
  .stat-card {
    padding: var(--space-16);
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-lg);
  }
  
  .stat-value {
    font-size: var(--font-size-2xl);
  }
  
  .playlists-grid,
  .videos-grid {
    grid-template-columns: 1fr;
    gap: var(--space-16);
    padding: var(--space-16);
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-16);
    padding: var(--space-16);
  }
  
  .section-header {
    padding: var(--space-16);
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-12);
  }
  
  .section-filters {
    justify-content: center;
  }
  
  .modal-content {
    margin: var(--space-16);
  }
  
  .toast-container {
    left: var(--space-16);
    right: var(--space-16);
  }
  
  .toast {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-12);
  }
  
  .dashboard-nav {
    justify-content: center;
  }
  
  .playlist-actions {
    flex-direction: column;
  }
  
  .goal-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-8);
  }
}

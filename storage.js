// Unified Storage Management for IsotopeAI - YT Tracker
// This module handles all data persistence using localStorage only

class StorageManager {
    constructor() {
        this.storageKey = 'ytTracker_data';
        this.settingsKey = 'ytTracker_settings';
        this.init();
    }

    init() {
        // Ensure storage structure exists
        if (!this.getData()) {
            this.saveData({
                playlists: [],
                currentPlaylist: null,
                settings: {
                    theme: 'auto',
                    autoSave: true,
                    notifications: true,
                    autoMarkComplete: true
                },
                analytics: {
                    totalWatchTime: 0,
                    currentStreak: 0,
                    lastActiveDate: null
                }
            });
        }
    }

    // Get all data
    getData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error reading from storage:', error);
            return null;
        }
    }

    // Save all data
    saveData(data) {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error saving to storage:', error);
            return false;
        }
    }

    // Get specific playlist by ID
    getPlaylist(playlistId) {
        const data = this.getData();
        if (!data || !data.playlists) return null;
        
        return data.playlists.find(p => p.id === playlistId);
    }

    // Save or update playlist
    savePlaylist(playlist) {
        const data = this.getData() || { playlists: [] };
        
        const existingIndex = data.playlists.findIndex(p => p.id === playlist.id);
        
        if (existingIndex >= 0) {
            data.playlists[existingIndex] = playlist;
        } else {
            data.playlists.push(playlist);
        }
        
        // Update current playlist if it's the same
        if (data.currentPlaylist && data.currentPlaylist.id === playlist.id) {
            data.currentPlaylist = playlist;
        }
        
        return this.saveData(data);
    }

    // Set current playlist
    setCurrentPlaylist(playlist) {
        const data = this.getData() || {};
        data.currentPlaylist = playlist;
        
        // Also ensure it's in the playlists array
        this.savePlaylist(playlist);
        
        return this.saveData(data);
    }

    // Get current playlist
    getCurrentPlaylist() {
        const data = this.getData();
        return data ? data.currentPlaylist : null;
    }

    // Update video progress
    updateVideoProgress(playlistId, videoId, progressData) {
        const data = this.getData();
        if (!data) return false;

        // Update in playlists array
        const playlist = data.playlists.find(p => p.id === playlistId);
        if (playlist) {
            const video = playlist.videos.find(v => v.id === videoId);
            if (video) {
                Object.assign(video, progressData);
                video.lastWatched = new Date().toISOString();
            }
        }

        // Update in current playlist if it matches
        if (data.currentPlaylist && data.currentPlaylist.id === playlistId) {
            const video = data.currentPlaylist.videos.find(v => v.id === videoId);
            if (video) {
                Object.assign(video, progressData);
                video.lastWatched = new Date().toISOString();
            }
        }

        return this.saveData(data);
    }

    // Mark video as complete
    markVideoComplete(playlistId, videoId) {
        return this.updateVideoProgress(playlistId, videoId, {
            completed: true,
            completionPercentage: 100,
            completedAt: new Date().toISOString()
        });
    }

    // Get all playlists
    getAllPlaylists() {
        const data = this.getData();
        return data ? data.playlists || [] : [];
    }

    // Delete playlist
    deletePlaylist(playlistId) {
        const data = this.getData();
        if (!data) return false;

        data.playlists = data.playlists.filter(p => p.id !== playlistId);
        
        // Clear current playlist if it was deleted
        if (data.currentPlaylist && data.currentPlaylist.id === playlistId) {
            data.currentPlaylist = null;
        }

        return this.saveData(data);
    }

    // Get settings
    getSettings() {
        const data = this.getData();
        return data ? data.settings || {} : {};
    }

    // Save settings
    saveSettings(settings) {
        const data = this.getData() || {};
        data.settings = { ...data.settings, ...settings };
        return this.saveData(data);
    }

    // Update analytics
    updateAnalytics(analyticsData) {
        const data = this.getData() || {};
        data.analytics = { ...data.analytics, ...analyticsData };
        return this.saveData(data);
    }

    // Get analytics
    getAnalytics() {
        const data = this.getData();
        return data ? data.analytics || {} : {};
    }

    // Export data for backup
    exportData() {
        const data = this.getData();
        if (!data) return null;

        return {
            ...data,
            exportedAt: new Date().toISOString(),
            version: '1.0.0'
        };
    }

    // Import data from backup
    importData(importedData) {
        try {
            // Validate imported data structure
            if (!importedData || typeof importedData !== 'object') {
                throw new Error('Invalid data format');
            }

            // Merge with existing data, keeping user preferences
            const currentData = this.getData() || {};
            const mergedData = {
                playlists: importedData.playlists || [],
                currentPlaylist: importedData.currentPlaylist || null,
                settings: { ...currentData.settings, ...importedData.settings },
                analytics: { ...currentData.analytics, ...importedData.analytics }
            };

            return this.saveData(mergedData);
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }

    // Clear all data (reset)
    clearAllData() {
        try {
            localStorage.removeItem(this.storageKey);
            this.init(); // Reinitialize with defaults
            return true;
        } catch (error) {
            console.error('Error clearing data:', error);
            return false;
        }
    }

    // Get storage usage info
    getStorageInfo() {
        try {
            const data = JSON.stringify(this.getData());
            const sizeInBytes = new Blob([data]).size;
            const sizeInKB = Math.round(sizeInBytes / 1024 * 100) / 100;
            
            return {
                sizeInBytes,
                sizeInKB,
                itemCount: this.getAllPlaylists().length
            };
        } catch (error) {
            console.error('Error getting storage info:', error);
            return null;
        }
    }
}

// Create global instance
window.storageManager = new StorageManager();